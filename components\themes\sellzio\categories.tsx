'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronLeft
} from 'lucide-react'
import SubcategoryView from './SubcategoryView'

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
}

// Fungsi untuk generate ID
const generateId = (prefix: string, name: string) => {
  return `${prefix}-${name.toLowerCase().replace(/\s+/g, '-')}`
}

// Fungsi untuk mendapatkan icon - menggunakan emoji seperti SubcategoryView
const getIcon = (iconName: string) => {
  const iconMap: { [key: string]: string } = {
    // Elektronik & Technology
    'device-mobile': '📱',
    'monitor': '💻',
    'camera': '📷',
    'headphones': '🎧',
    'tv': '📺',
    'gamepad': '🎮',
    'speaker': '🔊',
    'microwave': '🔥',
    'washing-machine': '🧺',
    'air-conditioner': '❄️',
    'lamp': '💡',
    'battery': '🔋',
    'thermometer': '🌡️',

    // Fashion & Accessories
    'shirt': '👕',
    'shoes': '👟',
    'bag': '🎒',
    'watch': '⌚',
    'ring': '💍',
    'glasses': '👓',
    'hat': '👒',
    'tie': '👔',

    // Health & Beauty
    'kesehatan': '🏥',
    'activity': '💊',
    'medicine': '💉',
    'first-aid': '🏥',
    'package': '📦',

    // Hobbies & Collections
    'star': '⭐',
    'music': '🎵',
    'archive': '📦',
    'pet': '🐕',
    'game': '🎲',
    'photo': '📸',

    // General
    'shopping-cart': '🛒',
    'shopping-bag': '🛍️',
    'home': '🏠',
    'car': '🚗',
    'book': '📚',
    'utensils': '🍽️',
    'gift': '🧸',
    'smile': '👶',
    'map-pin': '📍',
    'heart': '💄',

    // New category icons
    'smartphone': '📞',
    'tools': '🔧'
  }
  return iconMap[iconName] || '🛒'
}

// Fungsi untuk mendapatkan icon subcategory - unik dan spesifik
const getSubcategoryIcon = (subcategoryName: string) => {
  const subcategoryIcons: { [key: string]: string } = {
    // Elektronik - Icons yang lebih spesifik dan unik
    'Konsol Game': '🎮', 'Aksesoris Konsol': '🕹️', 'Alat Casing': '🔧', 'Foot Bath & Spa': '🛁',
    'Mesin Jahit & Aksesoris': '🧵', 'Setrika & Mesin Uap': '🔥', 'Purifier & Humidifier': '🌬️',
    'Perangkat Debu & Peralatan Perawatan Lantai': '🧹', 'Telepon': '☎️', 'Mesin Cuci & Pengering': '🧺',
    'Water Heater': '🚿', 'Pendingin Ruangan': '❄️', 'Pengering Sepatu': '👢', 'Penghangat Ruangan': '🔥',
    'TV & Aksesoris': '📺', 'Perangkat Dapur': '🍳', 'Lampu': '💡', 'Kamera Keamanan': '📹',
    'Video Game': '🎯', 'Kelastrian': '⚡', 'Baterai': '🔋', 'Rokok Elektronik & Shisha': '💨',
    'Remote Kontrol': '📱', 'Walkie Talkie': '📻', 'Media Player': '📀', 'Perangkat Audio & Speaker': '🎧',
    'Elektronik Lainnya': '⚙️',

    // Komputer & Aksesoris - Icons yang lebih beragam
    'Desktop': '🖥️', 'Monitor': '🖨️', 'Komponen Desktop & Laptop': '🔌', 'Penyimpanan Data': '💾',
    'Komponen Network': '🌐', 'Software': '💿', 'Peralatan Kantor': '📋', 'Printer & Scanner': '🖨️',
    'Aksesoris Desktop & Laptop': '🖱️', 'Keyboard & Mouse': '⌨️', 'Laptop': '💻', 'Gaming': '🎮',
    'Audio Computer': '🔊', 'Proyektor & Aksesoris': '📽️', 'Komputer & Aksesoris Lainnya': '💽',

    // Handphone & Aksesoris - Icons yang lebih spesifik
    'Kartu Perdana': '📶', 'Tablet': '📱', 'Handphone': '📞', 'Perangkat Wearable': '⌚',
    'Perangkat VR': '🥽', 'Aksesoris Selfie': '🤳', 'Handphone & Tablet Aksesoris': '📲',
    'Kartu Memori': '💳', 'Kabel, Charger, & Konverter': '🔌', 'Powerbank & Baterai': '🔋',
    'Casing & Skin': '📱', 'Audio Handphone': '🎵', 'Handphone & Aksesoris Lainnya': '📳',

    // Pakaian Pria - Icons yang lebih beragam
    'Denim': '👖', 'Hoodie & Sweatshirt': '🧥', 'Sweater & Cardigan': '🧶', 'Jaket, Mantel, & Rompi': '🧥',
    'Jas Formal': '🤵', 'Celana Panjang': '👖', 'Celana Pendek': '🩳', 'Atasan': '👕', 'Batik': '🎨',
    'Pakaian Dalam': '🩲', 'Pakaian Tidur': '🛌', 'Set Pakaian Pria': '👔', 'Pakaian Tradisional': '🥻',
    'Kostum': '🎭', 'Pakaian Kerja': '👷', 'Pakaian Pria Lainnya': '👨',

    // Sepatu Pria - Icons yang lebih spesifik
    'Sneakers': '👟', 'Kaos Kaki': '🧦', 'Sandal': '👡', 'Aksesoris & Perawatan Sepatu': '🧽',
    'Boot': '🥾', 'Tall Sepatu': '👢', 'Slip-On & Mules': '🥿', 'Sepatu Pria Lainnya': '👞',
    'Sepatu Formal': '👞',

    // Tas Pria - Icons yang lebih beragam
    'Tas Selempang & Bahu Pria': '🎒', 'Dompet': '💳', 'Ransel Pria': '🎒', 'Tas Pinggang Pria': '👝',
    'Tas Laptop': '💼', 'Clutch': '👛', 'Tote Bag': '🛍️', 'Tas Kerja': '💼', 'Tas Pria Lainnya': '🧳',

    // Aksesoris Fashion - Icons yang lebih spesifik
    'Cincin': '💍', 'Anting': '👂', 'Syal & Selendang': '🧣', 'Sarung Tangan': '🧤',
    'Aksesoris Rambut': '💇', 'Gelang Tangan & Bangle': '📿', 'Gelang Kaki': '🦶', 'Topi': '👒',
    'Kalung': '📿', 'Kacamata & Aksesoris': '👓', 'Lensa Kontak & Aksesoris': '👁️', 'Logam Mulia': '🥇',
    'Ikat Pinggang': '🔗', 'Dasi': '👔', 'Aksesoris Tambahan': '✨', 'Set & Paket Aksesoris': '🎁',
    'Perhiasan Berharga': '💎', 'Aksesoris Fashion Lainnya': '🎀',

    // Jam Tangan - Icons yang lebih beragam
    'Jam Tangan Wanita': '⌚', 'Jam Tangan Pria': '⏰', 'Jam Tangan Couple': '💕',
    'Aksesoris Jam Tangan': '🔗', 'Jam Tangan Lainnya': '⏱️',

    // Kesehatan - Icons yang lebih spesifik
    'Kewanitaan': '🌸', 'Kesehatan Seksual': '💗', 'Perawatan Mata': '👁️', 'Suplemen Makanan': '💊',
    'Obat-obatan & Alat Kesehatan': '🩺', 'Alat Tes & Monitor': '📊', 'P3K': '🚑',
    'Alat Bantu Cedera & Disabilitas': '🦽', 'Obat Nyamuk': '🦟', 'Popok Dewasa': '🧷',
    'Hand Sanitizer': '🧴', 'Minyak Esensial': '🌿', 'Perawatan Hidung & Pernapasan': '👃',
    'Perawatan Telinga': '👂', 'Perawatan Mulut': '🦷', 'Kesehatan Lainnya': '⚕️',

    // Hobi & Koleksi - Icons yang lebih beragam
    'Aksesoris Hewan Peliharaan': '🐕', 'Litter & Toilet': '🚽', 'Grooming Hewan': '✂️',
    'Pakaian & Aksesoris Hewan': '🦴', 'Perawatan Kesehatan Hewan': '🏥', 'Makanan Hewan': '🥫',
    'Koleksi': '🗃️', 'Mainan & Games': '🎲', 'CD, DVD & Bluray': '💿', 'Alat & Aksesoris Musik': '🎸',
    'Piringan Hitam': '📀', 'Album Foto': '📸', 'Perlengkapan Menjahit': '🧵', 'Hobi & Koleksi Lainnya': '🎨',

    'default': '🛒'
  };

  return subcategoryIcons[subcategoryName] || subcategoryIcons['default'];
}

// Data kategori - dengan icon yang unik dan sesuai
const allCategories: Category[] = [
  { id: generateId('kategori', 'elektronik'), name: "Elektronik", icon: 'tv', color: "#1BA0E2" },
  { id: generateId('kategori', 'komputer-aksesoris'), name: "Komputer & Aksesoris", icon: 'monitor', color: "#5D6D7E" },
  { id: generateId('kategori', 'handphone-aksesoris'), name: "Handphone & Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
  { id: generateId('kategori', 'pakaian-pria'), name: "Pakaian Pria", icon: 'shirt', color: "#F1C40F" },
  { id: generateId('kategori', 'sepatu-pria'), name: "Sepatu Pria", icon: 'shoes', color: "#E67E22" },
  { id: generateId('kategori', 'tas-pria'), name: "Tas Pria", icon: 'bag', color: "#8E44AD" },
  { id: generateId('kategori', 'aksesoris-fashion'), name: "Aksesoris Fashion", icon: 'ring', color: "#E84393" },
  { id: generateId('kategori', 'jam-tangan'), name: "Jam Tangan", icon: 'watch', color: "#F39C12" },
  { id: generateId('kategori', 'kesehatan'), name: "Kesehatan", icon: 'kesehatan', color: "#2ECC71" },
  { id: generateId('kategori', 'hobi-koleksi'), name: "Hobi & Koleksi", icon: 'pet', color: "#1ABC9C" }
]

// Data produk untuk setiap subcategory
const subcategoryProducts: { [key: string]: any[] } = {
  "Konsol Game": [
    { id: 1, name: "PlayStation 5", price: "Rp 7.999.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 1250 },
    { id: 2, name: "Xbox Series X", price: "Rp 7.499.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 890 },
    { id: 3, name: "Nintendo Switch OLED", price: "Rp 4.999.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 2100 },
    { id: 4, name: "Steam Deck", price: "Rp 8.999.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 450 },
    { id: 5, name: "PlayStation 4 Pro", price: "Rp 4.299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 3200 }
  ],
  "Aksesoris Konsol": [
    { id: 6, name: "DualSense Controller", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 5600 },
    { id: 7, name: "Xbox Wireless Controller", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 4200 },
    { id: 8, name: "Pro Controller Nintendo", price: "Rp 699.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 3100 },
    { id: 9, name: "Gaming Headset", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 2800 },
    { id: 10, name: "Charging Station", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1900 }
  ],
  "Alat Casing": [
    { id: 11, name: "PC Case RGB", price: "Rp 1.599.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 },
    { id: 12, name: "Tempered Glass Panel", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 13, name: "Cable Management Kit", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 750 },
    { id: 14, name: "Case Fan RGB", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1500 },
    { id: 15, name: "Motherboard Standoff", price: "Rp 99.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 650 }
  ],
  "Foot Bath & Spa": [
    { id: 16, name: "Foot Spa Massager", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1100 },
    { id: 17, name: "Ionic Foot Bath", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 780 },
    { id: 18, name: "Heated Foot Soaker", price: "Rp 699.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 920 },
    { id: 19, name: "Bubble Foot Bath", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 1350 },
    { id: 20, name: "Portable Foot Spa", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.2, sold: 1680 }
  ],
  "Mesin Jahit & Aksesoris": [
    { id: 21, name: "Singer Heavy Duty", price: "Rp 3.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 450 },
    { id: 22, name: "Brother XM2701", price: "Rp 2.899.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 620 },
    { id: 23, name: "Janome 2212", price: "Rp 1.999.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 380 },
    { id: 24, name: "Sewing Kit Complete", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 25, name: "Thread Set 50 Colors", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 890 }
  ],
  "Setrika & Mesin Uap": [
    { id: 26, name: "Philips Steam Iron", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1200 },
    { id: 27, name: "Tefal Steam Generator", price: "Rp 2.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 680 },
    { id: 28, name: "Panasonic Cordless Iron", price: "Rp 1.199.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 890 },
    { id: 29, name: "Black+Decker Steam Iron", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1450 },
    { id: 30, name: "Garment Steamer", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 1100 }
  ],
  "Purifier & Humidifier": [
    { id: 31, name: "Xiaomi Air Purifier", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 2100 },
    { id: 32, name: "Sharp Air Purifier", price: "Rp 2.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 1650 },
    { id: 33, name: "Ultrasonic Humidifier", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1890 },
    { id: 34, name: "HEPA Air Purifier", price: "Rp 1.599.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1320 },
    { id: 35, name: "Essential Oil Diffuser", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 2450 }
  ],
  "Perangkat Debu & Peralatan Perawatan Lantai": [
    { id: 36, name: "Dyson V15 Detect", price: "Rp 8.999.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 450 },
    { id: 37, name: "Shark Navigator", price: "Rp 2.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 },
    { id: 38, name: "Robot Vacuum", price: "Rp 3.599.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 1200 },
    { id: 39, name: "Wet Dry Vacuum", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 680 },
    { id: 40, name: "Steam Mop", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1350 }
  ],
  "Telepon": [
    { id: 41, name: "Panasonic Cordless Phone", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 890 },
    { id: 42, name: "Uniden DECT Phone", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 650 },
    { id: 43, name: "VTech Conference Phone", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 320 },
    { id: 44, name: "Gigaset Desk Phone", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1100 },
    { id: 45, name: "Wireless Intercom", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 780 }
  ],
  "Mesin Cuci & Pengering": [
    { id: 46, name: "LG Front Load Washer", price: "Rp 8.999.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 },
    { id: 47, name: "Samsung Top Load", price: "Rp 4.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 },
    { id: 48, name: "Electrolux Dryer", price: "Rp 6.599.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 320 },
    { id: 49, name: "Haier Twin Tub", price: "Rp 2.199.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 50, name: "Sharp Washer Dryer", price: "Rp 7.899.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 680 }
  ],
  "Water Heater": [
    { id: 51, name: "Ariston Electric Water Heater", price: "Rp 2.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 780 },
    { id: 52, name: "Rinnai Gas Water Heater", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 920 },
    { id: 53, name: "Modena Instant Water Heater", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1350 },
    { id: 54, name: "Wika Solar Water Heater", price: "Rp 4.599.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 },
    { id: 55, name: "Rheem Storage Water Heater", price: "Rp 3.299.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 650 }
  ],
  "Pendingin Ruangan": [
    { id: 56, name: "Daikin Split AC 1 PK", price: "Rp 3.899.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 1200 },
    { id: 57, name: "LG Dual Inverter AC", price: "Rp 4.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 890 },
    { id: 58, name: "Sharp Portable AC", price: "Rp 5.599.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 450 },
    { id: 59, name: "Panasonic Window AC", price: "Rp 2.899.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 780 },
    { id: 60, name: "Mitsubishi Ceiling AC", price: "Rp 6.999.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 320 }
  ],
  "Pengering Sepatu": [
    { id: 61, name: "Xiaomi Shoe Dryer", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 62, name: "UV Shoe Sterilizer", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 890 },
    { id: 63, name: "Electric Shoe Warmer", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 1500 },
    { id: 64, name: "Portable Shoe Dryer", price: "Rp 99.000", image: "/api/placeholder/200/200", rating: 4.2, sold: 2100 },
    { id: 65, name: "Boot Dryer Rack", price: "Rp 79.000", image: "/api/placeholder/200/200", rating: 4.1, sold: 1800 }
  ],
  "Penghangat Ruangan": [
    { id: 66, name: "Miyako Room Heater", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 650 },
    { id: 67, name: "Sharp Oil Heater", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 450 },
    { id: 68, name: "Cosmos Fan Heater", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 890 },
    { id: 69, name: "Denpoo Ceramic Heater", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1100 },
    { id: 70, name: "Krisbow Infrared Heater", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 780 }
  ],
  "TV & Aksesoris": [
    { id: 71, name: "Samsung 55 inch Smart TV", price: "Rp 8.999.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 },
    { id: 72, name: "LG OLED 65 inch", price: "Rp 15.999.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 180 },
    { id: 73, name: "Sony Bravia 43 inch", price: "Rp 6.299.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 680 },
    { id: 74, name: "TCL Android TV 32 inch", price: "Rp 2.899.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 75, name: "Chromecast with Google TV", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 }
  ],
  "Perangkat Dapur": [
    { id: 76, name: "Philips Air Fryer", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 1500 },
    { id: 77, name: "Sharp Microwave", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 },
    { id: 78, name: "Miyako Rice Cooker", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 2100 },
    { id: 79, name: "Oxone Blender", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1350 },
    { id: 80, name: "Electrolux Induction Cooker", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 780 }
  ],
  "Lampu": [
    { id: 81, name: "Philips LED Bulb 12W", price: "Rp 89.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 3200 },
    { id: 82, name: "Xiaomi Smart Bulb", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 1800 },
    { id: 83, name: "Osram LED Strip", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 84, name: "Hannochs Emergency Lamp", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 2500 },
    { id: 85, name: "Panasonic Ceiling Light", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 650 }
  ],
  "Kamera Keamanan": [
    { id: 86, name: "Xiaomi Security Camera", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1200 },
    { id: 87, name: "Hikvision CCTV", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 680 },
    { id: 88, name: "TP-Link Tapo Cam", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1500 },
    { id: 89, name: "Ezviz Outdoor Camera", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 890 },
    { id: 90, name: "Dahua IP Camera", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 }
  ],
  "Video Game": [
    { id: 91, name: "FIFA 24 PS5", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 1200 },
    { id: 92, name: "Call of Duty MW3", price: "Rp 999.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 890 },
    { id: 93, name: "Spider-Man 2 PS5", price: "Rp 799.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 1500 },
    { id: 94, name: "Zelda Tears Kingdom", price: "Rp 699.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 680 },
    { id: 95, name: "Elden Ring", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 2100 }
  ],
  "Kelastrian": [
    { id: 96, name: "Stop Kontak Universal", price: "Rp 89.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 2500 },
    { id: 97, name: "Kabel Extension 10M", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1800 },
    { id: 98, name: "MCB Schneider 10A", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 1200 },
    { id: 99, name: "Stabilizer Matsunaga", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 650 },
    { id: 100, name: "UPS APC 650VA", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 }
  ],
  "Baterai": [
    { id: 101, name: "Energizer AA Battery", price: "Rp 49.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 3500 },
    { id: 102, name: "Panasonic Lithium CR2032", price: "Rp 29.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 2800 },
    { id: 103, name: "Duracell 9V Battery", price: "Rp 89.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1200 },
    { id: 104, name: "ABC Alkaline AAA", price: "Rp 39.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 2100 },
    { id: 105, name: "Varta Rechargeable AA", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 890 }
  ],
  "Rokok Elektronik & Shisha": [
    { id: 106, name: "RELX Pod Vape", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 890 },
    { id: 107, name: "JUUL Starter Kit", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 650 },
    { id: 108, name: "Vaporesso Pod", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 780 },
    { id: 109, name: "Shisha Electric", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 320 },
    { id: 110, name: "E-Liquid Premium", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.3, sold: 1200 }
  ],
  "Remote Kontrol": [
    { id: 111, name: "Universal TV Remote", price: "Rp 89.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1500 },
    { id: 112, name: "AC Remote Control", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1200 },
    { id: 113, name: "Set Top Box Remote", price: "Rp 99.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 890 },
    { id: 114, name: "Smart TV Remote", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 650 },
    { id: 115, name: "Learning Remote", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 }
  ],
  "Walkie Talkie": [
    { id: 116, name: "Baofeng UV-5R", price: "Rp 399.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 780 },
    { id: 117, name: "Motorola T82", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 450 },
    { id: 118, name: "Kenwood TK-3107", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 320 },
    { id: 119, name: "Icom IC-V8", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 180 },
    { id: 120, name: "Yaesu VX-6R", price: "Rp 2.599.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 120 }
  ],
  "Media Player": [
    { id: 121, name: "Apple TV 4K", price: "Rp 2.899.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 },
    { id: 122, name: "Nvidia Shield TV", price: "Rp 3.299.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 320 },
    { id: 123, name: "Roku Ultra", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 680 },
    { id: 124, name: "Amazon Fire TV Stick", price: "Rp 899.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 1200 },
    { id: 125, name: "Mi TV Stick", price: "Rp 599.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 1500 }
  ],
  "Perangkat Audio & Speaker": [
    { id: 126, name: "JBL Charge 5", price: "Rp 2.299.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 890 },
    { id: 127, name: "Sony WH-1000XM4", price: "Rp 4.999.000", image: "/api/placeholder/200/200", rating: 4.9, sold: 450 },
    { id: 128, name: "Bose SoundLink", price: "Rp 1.899.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 680 },
    { id: 129, name: "Audio Technica ATH-M50x", price: "Rp 2.599.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 320 },
    { id: 130, name: "Harman Kardon Onyx", price: "Rp 3.299.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 250 }
  ],
  "Elektronik Lainnya": [
    { id: 131, name: "Digital Multimeter", price: "Rp 299.000", image: "/api/placeholder/200/200", rating: 4.5, sold: 650 },
    { id: 132, name: "Soldering Iron Kit", price: "Rp 199.000", image: "/api/placeholder/200/200", rating: 4.6, sold: 890 },
    { id: 133, name: "Power Supply 12V", price: "Rp 149.000", image: "/api/placeholder/200/200", rating: 4.4, sold: 1200 },
    { id: 134, name: "Arduino Uno R3", price: "Rp 189.000", image: "/api/placeholder/200/200", rating: 4.7, sold: 780 },
    { id: 135, name: "Raspberry Pi 4", price: "Rp 1.299.000", image: "/api/placeholder/200/200", rating: 4.8, sold: 450 }
  ]
};

// Data subkategori - sesuai data yang diberikan
const subCategories: { [key: string]: Category[] } = {
  "Elektronik": [
    { id: generateId('elektronik', 'konsol-game'), name: "Konsol Game", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'aksesoris-konsol'), name: "Aksesoris Konsol", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'alat-casing'), name: "Alat Casing", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'foot-bath-spa'), name: "Foot Bath & Spa", icon: 'health', color: "#1BA0E2" },
    { id: generateId('elektronik', 'mesin-jahit'), name: "Mesin Jahit & Aksesoris", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'setrika'), name: "Setrika & Mesin Uap", icon: 'microwave', color: "#1BA0E2" },
    { id: generateId('elektronik', 'purifier'), name: "Purifier & Humidifier", icon: 'air-conditioner', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-debu'), name: "Perangkat Debu & Peralatan Perawatan Lantai", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'telepon'), name: "Telepon", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'mesin-cuci'), name: "Mesin Cuci & Pengering", icon: 'washing-machine', color: "#1BA0E2" },
    { id: generateId('elektronik', 'water-heater'), name: "Water Heater", icon: 'thermometer', color: "#1BA0E2" },
    { id: generateId('elektronik', 'pendingin-ruangan'), name: "Pendingin Ruangan", icon: 'air-conditioner', color: "#1BA0E2" },
    { id: generateId('elektronik', 'pengering-sepatu'), name: "Pengering Sepatu", icon: 'shoes', color: "#1BA0E2" },
    { id: generateId('elektronik', 'penghangat-ruangan'), name: "Penghangat Ruangan", icon: 'thermometer', color: "#1BA0E2" },
    { id: generateId('elektronik', 'tv-aksesoris'), name: "TV & Aksesoris", icon: 'tv', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-dapur'), name: "Perangkat Dapur", icon: 'microwave', color: "#1BA0E2" },
    { id: generateId('elektronik', 'lampu'), name: "Lampu", icon: 'lamp', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kamera-keamanan'), name: "Kamera Keamanan", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('elektronik', 'video-game'), name: "Video Game", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kelastrian'), name: "Kelastrian", icon: 'battery', color: "#1BA0E2" },
    { id: generateId('elektronik', 'baterai'), name: "Baterai", icon: 'battery', color: "#1BA0E2" },
    { id: generateId('elektronik', 'rokok-elektronik'), name: "Rokok Elektronik & Shisha", icon: 'health', color: "#1BA0E2" },
    { id: generateId('elektronik', 'remote-kontrol'), name: "Remote Kontrol", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'walkie-talkie'), name: "Walkie Talkie", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'media-player'), name: "Media Player", icon: 'tv', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-audio'), name: "Perangkat Audio & Speaker", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('elektronik', 'elektronik-lainnya'), name: "Elektronik Lainnya", icon: 'device-mobile', color: "#1BA0E2" }
  ],
  "Komputer & Aksesoris": [
    { id: generateId('komputer', 'desktop'), name: "Desktop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'monitor'), name: "Monitor", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'komponen-desktop'), name: "Komponen Desktop & Laptop", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'penyimpanan-data'), name: "Penyimpanan Data", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'komponen-network'), name: "Komponen Network", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'software'), name: "Software", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'peralatan-kantor'), name: "Peralatan Kantor", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'printer-scanner'), name: "Printer & Scanner", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'aksesoris-desktop'), name: "Aksesoris Desktop & Laptop", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'keyboard-mouse'), name: "Keyboard & Mouse", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'laptop'), name: "Laptop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'gaming'), name: "Gaming", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'audio-computer'), name: "Audio Computer", icon: 'headphones', color: "#5D6D7E" },
    { id: generateId('komputer', 'proyektor'), name: "Proyektor & Aksesoris", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'komputer-lainnya'), name: "Komputer & Aksesoris Lainnya", icon: 'device-mobile', color: "#5D6D7E" }
  ],
  "Handphone & Aksesoris": [
    { id: generateId('handphone', 'kartu-perdana'), name: "Kartu Perdana", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'tablet'), name: "Tablet", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone'), name: "Handphone", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'perangkat-wearable'), name: "Perangkat Wearable", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'perangkat-vr'), name: "Perangkat VR", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'aksesoris-selfie'), name: "Aksesoris Selfie", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone-tablet-aksesoris'), name: "Handphone & Tablet Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'kartu-memori'), name: "Kartu Memori", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'kabel-charger'), name: "Kabel, Charger, & Konverter", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'powerbank'), name: "Powerbank & Baterai", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'casing-skin'), name: "Casing & Skin", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'audio-handphone'), name: "Audio Handphone", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone-lainnya'), name: "Handphone & Aksesoris Lainnya", icon: 'device-mobile', color: "#1BA0E2" }
  ],
  "Pakaian Pria": [
    { id: generateId('pakaian-pria', 'denim'), name: "Denim", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'hoodie'), name: "Hoodie & Sweatshirt", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'sweater'), name: "Sweater & Cardigan", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'jaket'), name: "Jaket, Mantel, & Rompi", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'jas-formal'), name: "Jas Formal", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'celana-panjang'), name: "Celana Panjang", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'celana-pendek'), name: "Celana Pendek", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'atasan'), name: "Atasan", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'batik'), name: "Batik", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-dalam'), name: "Pakaian Dalam", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-tidur'), name: "Pakaian Tidur", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'set-pakaian'), name: "Set Pakaian Pria", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-tradisional'), name: "Pakaian Tradisional", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'kostum'), name: "Kostum", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-kerja'), name: "Pakaian Kerja", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-pria-lainnya'), name: "Pakaian Pria Lainnya", icon: 'shirt', color: "#F1C40F" }
  ],
  "Sepatu Pria": [
    { id: generateId('sepatu-pria', 'sneakers'), name: "Sneakers", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'kaos-kaki'), name: "Kaos Kaki", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sandal'), name: "Sandal", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'aksesoris-perawatan'), name: "Aksesoris & Perawatan Sepatu", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'boot'), name: "Boot", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'tall-sepatu'), name: "Tall Sepatu", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'slip-on'), name: "Slip-On & Mules", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sepatu-pria-lainnya'), name: "Sepatu Pria Lainnya", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sepatu-formal'), name: "Sepatu Formal", icon: 'shopping-cart', color: "#E67E22" }
  ],
  "Tas Pria": [
    { id: generateId('tas-pria', 'tas-selempang'), name: "Tas Selempang & Bahu Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'dompet'), name: "Dompet", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'ransel'), name: "Ransel Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-pinggang'), name: "Tas Pinggang Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-laptop'), name: "Tas Laptop", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'clutch'), name: "Clutch", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tote-bag'), name: "Tote Bag", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-kerja'), name: "Tas Kerja", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-pria-lainnya'), name: "Tas Pria Lainnya", icon: 'shopping-cart', color: "#8E44AD" }
  ],
  "Aksesoris Fashion": [
    { id: generateId('aksesoris', 'cincin'), name: "Cincin", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'anting'), name: "Anting", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'syal'), name: "Syal & Selendang", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'sarung-tangan'), name: "Sarung Tangan", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-rambut'), name: "Aksesoris Rambut", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'gelang-tangan'), name: "Gelang Tangan & Bangle", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'gelang-kaki'), name: "Gelang Kaki", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'topi'), name: "Topi", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'kalung'), name: "Kalung", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'kacamata'), name: "Kacamata & Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'lensa-kontak'), name: "Lensa Kontak & Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'logam-mulia'), name: "Logam Mulia", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'ikat-pinggang'), name: "Ikat Pinggang", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'dasi'), name: "Dasi", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-tambahan'), name: "Aksesoris Tambahan", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'set-paket'), name: "Set & Paket Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'perhiasan-berharga'), name: "Perhiasan Berharga", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-lainnya'), name: "Aksesoris Fashion Lainnya", icon: 'shopping-cart', color: "#E84393" }
  ],
  "Jam Tangan": [
    { id: generateId('jam-tangan', 'jam-wanita'), name: "Jam Tangan Wanita", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-pria'), name: "Jam Tangan Pria", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-couple'), name: "Jam Tangan Couple", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'aksesoris-jam'), name: "Aksesoris Jam Tangan", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-lainnya'), name: "Jam Tangan Lainnya", icon: 'shopping-cart', color: "#F39C12" }
  ],
  "Kesehatan": [
    { id: generateId('kesehatan', 'kewanitaan'), name: "Kewanitaan", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'kesehatan-seksual'), name: "Kesehatan Seksual", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-mata'), name: "Perawatan Mata", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'suplemen'), name: "Suplemen Makanan", icon: 'package', color: "#2ECC71" },
    { id: generateId('kesehatan', 'obat-obatan'), name: "Obat-obatan & Alat Kesehatan", icon: 'thermometer', color: "#2ECC71" },
    { id: generateId('kesehatan', 'alat-tes'), name: "Alat Tes & Monitor", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'p3k'), name: "P3K", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'alat-bantu'), name: "Alat Bantu Cedera & Disabilitas", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'obat-nyamuk'), name: "Obat Nyamuk", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'popok-dewasa'), name: "Popok Dewasa", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'hand-sanitizer'), name: "Hand Sanitizer", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'minyak-esensial'), name: "Minyak Esensial", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-hidung'), name: "Perawatan Hidung & Pernapasan", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-telinga'), name: "Perawatan Telinga", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-mulut'), name: "Perawatan Mulut", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'kesehatan-lainnya'), name: "Kesehatan Lainnya", icon: 'activity', color: "#2ECC71" }
  ],
  "Hobi & Koleksi": [
    { id: generateId('hobi', 'aksesoris-hewan'), name: "Aksesoris Hewan Peliharaan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'litter-toilet'), name: "Litter & Toilet", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'grooming-hewan'), name: "Grooming Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'pakaian-hewan'), name: "Pakaian & Aksesoris Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'perawatan-kesehatan-hewan'), name: "Perawatan Kesehatan Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'makanan-hewan'), name: "Makanan Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'koleksi'), name: "Koleksi", icon: 'archive', color: "#1ABC9C" },
    { id: generateId('hobi', 'mainan-games'), name: "Mainan & Games", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'cd-dvd'), name: "CD, DVD & Bluray", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'alat-musik'), name: "Alat & Aksesoris Musik", icon: 'music', color: "#1ABC9C" },
    { id: generateId('hobi', 'piringan-hitam'), name: "Piringan Hitam", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'album-foto'), name: "Album Foto", icon: 'camera', color: "#1ABC9C" },
    { id: generateId('hobi', 'perlengkapan-menjahit'), name: "Perlengkapan Menjahit", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'hobi-lainnya'), name: "Hobi & Koleksi Lainnya", icon: 'star', color: "#1ABC9C" }
  ]
}

// Komponen CategoryItem - icon sama untuk mobile dan desktop
const CategoryItem = ({ category, onClick, isExpandedView = false }: {
  category: Category,
  onClick?: (e: React.MouseEvent<Element>) => void,
  isExpandedView?: boolean
}) => (
  <motion.div
    className={`
      flex flex-col items-center justify-center rounded-lg transition-all duration-200
      border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
      ${isExpandedView ? 'p-2 w-[100px] h-[100px]' : 'p-1.5 w-[80px] h-[80px] mx-0.5'}
      overflow-hidden cursor-pointer
    `}
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={(e: React.MouseEvent) => {
      e.preventDefault()
      onClick?.(e)
    }}
  >
    <div className={`flex items-center justify-center ${isExpandedView ? 'mb-2' : 'mb-1'}`} style={{ height: '32px', width: '100%' }}>
      <div
        className="flex items-center justify-center w-8 h-8 text-2xl"
        style={{ minWidth: '32px' }}
      >
        {getIcon(category.icon)}
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className={`
        text-center text-gray-800 leading-tight break-words
        ${isExpandedView ? 'text-xs' : 'text-[10px] line-clamp-2'}
        w-full
      `}>
        {category.name}
      </span>
    </div>
  </motion.div>
)

// Komponen SeeAllItem
const SeeAllItem = ({ onClick, isExpanded }: { onClick: (e: React.MouseEvent) => void, isExpanded: boolean }) => (
  <motion.div
    className="flex flex-col items-center justify-center rounded-lg transition-all duration-200
               border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
               p-1.5 w-[80px] h-[80px] mx-0.5 overflow-hidden cursor-pointer"
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    <div className="flex items-center justify-center mb-1" style={{ height: '32px', width: '100%' }}>
      <div className="flex items-center justify-center w-8 h-8 text-2xl" style={{ minWidth: '32px' }}>
        ⊡
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className="text-center text-gray-800 leading-tight break-words text-[10px] line-clamp-2 w-full">
        Lihat Semua
      </span>
    </div>
  </motion.div>
)

export default function SellzioCategories() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [visibleCategories, setVisibleCategories] = useState(allCategories.slice(0, 9))
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [showSubcategoryView, setShowSubcategoryView] = useState(false)
  const [sortedCategories, setSortedCategories] = useState(allCategories)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialShowTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const showIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const mobileContainerRef = useRef<HTMLDivElement>(null)

  // Detect client-side and mobile
  useEffect(() => {
    setIsClient(true)
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < 768)
      }
    }

    checkMobile()
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', checkMobile)
      return () => window.removeEventListener('resize', checkMobile)
    }
  }, [])

  // Set up initial show timeout dan interval untuk scroll hint
  useEffect(() => {
    if (isMobile) {
      // Tampilkan tombol pertama kali setelah 7 detik
      initialShowTimeoutRef.current = setTimeout(() => {
        if (!isExpanded) {
          showViewAllButton();
        }

        // Set interval untuk menampilkan tombol setiap 15 detik
        showIntervalRef.current = setInterval(() => {
          if (!isScrolling && !isExpanded) {
            showViewAllButton();
          }
        }, 15000);
      }, 7000);

      // Add scroll event listener
      const container = mobileContainerRef.current;
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }

      // Cleanup function
      return () => {
        if (initialShowTimeoutRef.current) clearTimeout(initialShowTimeoutRef.current);
        if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
        if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
        if (showIntervalRef.current) clearInterval(showIntervalRef.current);
        if (container) container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isMobile, isScrolling, isExpanded])

  // Fungsi untuk menangani klik kategori - sama seperti Velozio
  const handleCategoryClick = (e: React.MouseEvent, category: Category) => {
    e.preventDefault();
    if (!category) return;

    // Untuk tampilan mobile, buka subcategory view
    if (isMobile) {
      // Update urutan kategori dengan yang dipilih di paling atas
      const newSortedCategories = [
        category,
        ...allCategories.filter((cat: Category) => cat.id !== category.id)
      ];
      setSortedCategories(newSortedCategories);

      setSelectedCategory(category);
      setShowSubcategoryView(true);
      // Disable scrolling pada body saat subcategory view terbuka
      if (typeof document !== 'undefined') {
        document.body.style.overflow = 'hidden';
      }
      return;
    }

    // Update urutan kategori dengan yang dipilih di paling atas untuk semua device
    const newSortedCategories = [
      category,
      ...allCategories.filter((cat: Category) => cat.id !== category.id)
    ];
    setSortedCategories(newSortedCategories);

    const hasSubcategories = category.name in subCategories &&
      subCategories[category.name]?.length > 0;

    if (hasSubcategories) {
      setSelectedCategory(category);
    } else {
      // Jika tidak ada subkategori, lakukan aksi default
      console.log('Kategori dipilih:', category.name);
      // Tambahkan logika navigasi atau tindakan lain yang diperlukan
    }
  };

  // Fungsi untuk toggle expand - sama seperti Velozio
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();

    if (isMobile) {
      // Jika di mobile, langsung arahkan ke subcategory view dengan kategori pertama
      if (allCategories.length > 0) {
        const firstCategory = allCategories[0];
        const newSortedCategories = [
          firstCategory,
          ...allCategories.filter((cat: Category) => cat.id !== firstCategory.id)
        ];
        setSortedCategories(newSortedCategories);
        setSelectedCategory(firstCategory);
        setShowSubcategoryView(true);
        if (typeof document !== 'undefined') {
          document.body.style.overflow = 'hidden';
        }
      }
      return;
    }

    // Untuk desktop/tablet, tetap gunakan logika expand biasa
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    if (newExpandedState && mobileContainerRef.current) {
      mobileContainerRef.current.scrollLeft = 0;
    }

    setVisibleCategories(newExpandedState ? allCategories : allCategories.slice(0, 9));
  };

  // Fungsi untuk menutup subcategory view
  const handleCloseSubcategoryView = () => {
    setShowSubcategoryView(false);
    // Enable scrolling kembali pada body
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
    }
  };

  // Mengambil daftar subkategori
  const getSubcategories = (categoryName: string) => {
    if (!categoryName || !(categoryName in subCategories)) {
      console.log('Kategori tidak ditemukan atau tidak valid:', categoryName);
      return [];
    }
    return subCategories[categoryName];
  };

  // Mengambil produk untuk subcategory tertentu
  const getSubcategoryProducts = (subcategoryName: string) => {
    return subcategoryProducts[subcategoryName] || [];
  };

  // Fungsi untuk menangani klik tombol kembali
  const handleBackClick = () => {
    setSelectedCategory(null);
  };

  // Fungsi untuk menangani klik subkategori
  const handleSubcategoryClick = (subcategory: Category) => {
    if (!subcategory) return;

    console.log('Subkategori dipilih:', subcategory.name);

    // Tutup subcategory view terlebih dahulu
    const currentCategory = selectedCategory;
    setSelectedCategory(null);
    setShowSubcategoryView(false);
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
    }

    // Trigger search di halaman utama dengan subcategory name
    // Kita perlu memanggil fungsi search dari parent component
    if (typeof window !== 'undefined') {
      const allSubs = getSubcategories(currentCategory?.name || '');
      const products = getSubcategoryProducts(subcategory.name);
      console.log('Categories - Dispatching event with data:', {
        query: subcategory.name,
        category: currentCategory?.name,
        selectedSubcategory: subcategory.name,
        allSubcategories: allSubs,
        products: products
      });

      // Dispatch custom event untuk komunikasi dengan parent
      const searchEvent = new CustomEvent('subcategorySearch', {
        detail: {
          query: subcategory.name,
          category: currentCategory?.name,
          selectedSubcategory: subcategory.name,
          allSubcategories: allSubs,
          products: products
        }
      });
      window.dispatchEvent(searchEvent);
    }
  };

  // Animasi scroll hint untuk mobile
  const mobileScrollHintAnimation = {
    x: [0, 3, 0],
    transition: {
      repeat: Infinity,
      duration: 1.5,
      repeatType: "loop" as const,
      ease: "easeInOut"
    }
  };

  // Fungsi untuk menampilkan tombol View All
  const showViewAllButton = () => {
    // Jangan tampilkan tombol jika dalam mode expanded
    if (isExpanded) {
      setShowScrollHint(false);
      return;
    }

    // Hapus timeout yang ada
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Tampilkan tombol
    setShowScrollHint(true);

    // Sembunyikan setelah 5 detik
    hideTimeoutRef.current = setTimeout(() => {
      setShowScrollHint(false);
    }, 5000);
  };

  // Handle scroll untuk mobile
  const handleScroll = () => {
    if (!mobileContainerRef.current || isExpanded) return;

    const { scrollLeft, scrollWidth, clientWidth } = mobileContainerRef.current;
    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 5; // Sedikit toleransi

    // Sembunyikan tombol View All saat scroll
    setShowScrollHint(false);
    setIsScrolling(true);

    // Hapus semua timeout yang ada
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Set timeout untuk menampilkan tombol View All setelah 15 detik tidak ada scroll
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
      if (!isAtEnd && !isExpanded) { // Hanya tampilkan jika tidak di ujung kanan dan tidak expanded
        showViewAllButton();
      }
    }, 15000);
  };

  // Prevent hydration mismatch by not rendering until client-side
  if (!isClient) {
    return (
      <div className="sellzio-categories-section">
        <div className="container mx-auto max-w-6xl px-0 overflow-visible">
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
              />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="sellzio-categories-section">
      {/* Desktop Subcategory View */}
      {selectedCategory && !isMobile && (
        <div
          className="desktop-subcategory-overlay"
          onClick={(e) => {
            // Close popup jika click di luar container
            if (e.target === e.currentTarget) {
              setSelectedCategory(null);
            }
          }}
        >
          <div className="subcategory-container">
            {/* Sidebar Kategori dengan Sticky Header */}
            <div className="category-sidebar">
              {/* Sticky Header di atas kategori */}
              <div className="sidebar-sticky-header">
                <button className="back-button" onClick={handleBackClick}>
                  <ChevronLeft size={20} />
                  <span>Kembali</span>
                </button>
              </div>

              {/* Daftar Kategori dengan Reordering untuk Sidebar */}
              <div className="sidebar-categories">
                {sortedCategories.map((cat: Category) => (
                  <div
                    key={cat.id}
                    className={`sidebar-item ${cat.id === selectedCategory?.id ? 'active' : ''}`}
                    onClick={() => {
                      // Update urutan kategori hanya untuk sidebar
                      const newSortedCategories = [
                        cat,
                        ...sortedCategories.filter((c: Category) => c.id !== cat.id)
                      ];
                      setSortedCategories(newSortedCategories);
                      setSelectedCategory(cat);
                    }}
                  >
                    <div className="sidebar-icon text-xl">
                      {getIcon(cat.icon)}
                    </div>
                    <div className="sidebar-text">{cat.name}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Content Area */}
            <div className="subcategory-content">
              {/* Header dengan Close Button */}
              <div className="subcategory-header">
                <h3 className="subcategory-title">{selectedCategory.name}</h3>
              </div>

              {/* Close Button di pojok kanan atas */}
              <button
                className="close-button-top-right"
                onClick={() => setSelectedCategory(null)}
                aria-label="Tutup"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>

              {/* Subcategory Grid */}
              <div className="subcategory-grid">
                {getSubcategories(selectedCategory.name).map((subCategory: Category, idx: number) => {
                  return (
                    <div
                      key={idx}
                      className="subcategory-item"
                      onClick={() => handleSubcategoryClick(subCategory)}
                    >
                      <div className="subcategory-icon text-2xl">
                        {getSubcategoryIcon(subCategory.name)}
                      </div>
                      <div className="subcategory-title">
                        {subCategory.name}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto max-w-6xl px-0 overflow-visible">
        {/* Mobile View */}
        {isMobile ? (
          <>
            <div className={`relative categories-container-wrapper ${!isExpanded ? 'has-blur' : ''}`}>
              {!isExpanded && (
                <div
                  ref={mobileContainerRef}
                  className="flex overflow-x-auto pb-3 hide-scrollbar categories-container"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                    WebkitOverflowScrolling: 'touch',
                    paddingRight: '5px'
                  }}
                >
                  <div className="flex gap-2 relative pr-4">
                    <AnimatePresence>
                      {visibleCategories.map((category, index) => (
                        <CategoryItem
                          key={index}
                          category={category}
                          onClick={(e) => handleCategoryClick(e, category)}
                        />
                      ))}
                      {!isExpanded && (
                        <div className="pr-2">
                          <SeeAllItem
                            onClick={(e: React.MouseEvent) => toggleExpand(e)}
                            isExpanded={isExpanded}
                          />
                        </div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Animasi panah untuk scroll hint di mobile */}
                  {showScrollHint && !isScrolling && (
                    <div
                      className="absolute right-2 top-[45%] -translate-y-1/2 z-10 h-8 bg-[rgba(255,245,240,0.95)] border border-[#FFDFD1] rounded-[16px] shadow-md flex items-center px-2 py-0 cursor-pointer"
                      onClick={(e: React.MouseEvent) => toggleExpand(e)}
                    >
                      <span className="text-[11px] font-semibold text-[#FF5722] mr-1">View All</span>
                      <motion.div
                        className="flex items-center justify-center text-[#FF5722]"
                        animate={mobileScrollHintAnimation}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </motion.div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Mobile Expanded View */}
            {isExpanded && !showSubcategoryView && (
              <div
                className="fixed inset-0 bg-white z-50 overflow-y-auto"
                style={{ paddingTop: '60px' }}
              >
                <div
                  className="grid grid-flow-col auto-cols-[100px] gap-3 justify-start w-max mx-auto"
                  style={{
                    gridAutoFlow: 'column',
                    gridTemplateRows: 'repeat(2, 100px)',
                    padding: '0.5rem 1rem',
                    margin: '0 auto',
                    width: 'auto',
                    minWidth: '100%',
                    display: 'inline-grid'
                  } as React.CSSProperties}
                >
                  <AnimatePresence>
                    {allCategories.map((category, index) => (
                      <CategoryItem
                        key={index}
                        category={category}
                        isExpandedView={true}
                        onClick={(e) => handleCategoryClick(e, category)}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            )}

            {/* Mobile Subcategory View */}
            {showSubcategoryView && selectedCategory && (
              <SubcategoryView
                category={selectedCategory}
                subcategories={getSubcategories(selectedCategory.name)}
                allCategories={allCategories}
                onBack={handleCloseSubcategoryView}
                onSubcategoryClick={handleSubcategoryClick}
                onCategoryChange={(newCategory) => {
                  setSelectedCategory(newCategory);
                  // Update urutan kategori dengan yang dipilih di paling atas
                  const newSortedCategories = [
                    newCategory,
                    ...allCategories.filter((cat: Category) => cat.id !== newCategory.id)
                  ];
                  setSortedCategories(newSortedCategories);
                }}
              />
            )}
          </>
        ) : (
          /* Desktop View */
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
